import React from 'react';
import { Bell, User, Settings } from 'lucide-react';

const NotaHubHeader = () => {
  return (
    <header className="bg-gradient-to-r from-slate-50 to-gray-100 border-b-2 border-orange-400 shadow-lg">
      <div className="px-6 py-4">
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-6">
            <img
              src="/assets/NotaHubTransparente.png"
              alt="NotaHub"
              className="h-16 w-auto"
            />
            <div>
              <p className="text-slate-600 text-sm font-medium">Automação Inteligente de Documentos Fiscais</p>
            </div>
          </div>
          <div className="flex items-center space-x-6">
            <div className="flex items-center space-x-2 bg-green-50 border border-green-200 px-3 py-1 rounded-full">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-sm text-green-700 font-medium">Sistema Online</span>
            </div>
            <button className="relative p-2 hover:bg-orange-50 rounded-lg transition-colors text-slate-600">
              <Bell className="w-6 h-6" />
              <span className="absolute -top-1 -right-1 bg-orange-500 text-white text-xs w-5 h-5 rounded-full flex items-center justify-center">3</span>
            </button>
            <button className="p-2 hover:bg-orange-50 rounded-lg transition-colors text-slate-600">
              <Settings className="w-6 h-6" />
            </button>
            <div className="flex items-center space-x-3 pl-4 border-l border-gray-300">
              <div className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center">
                <User className="w-5 h-5 text-white" />
              </div>
              <div>
                <p className="text-sm font-medium text-slate-700">Wesley Souza</p>
                <p className="text-xs text-slate-500">Administrador</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};

export default NotaHubHeader;